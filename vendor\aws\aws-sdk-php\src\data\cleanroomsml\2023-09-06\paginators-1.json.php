<?php
// This file was auto-generated from sdk-root/src/data/cleanroomsml/2023-09-06/paginators-1.json
return [ 'pagination' => [ 'ListAudienceExportJobs' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'audienceExportJobs', ], 'ListAudienceGenerationJobs' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'audienceGenerationJobs', ], 'ListAudienceModels' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'audienceModels', ], 'ListCollaborationConfiguredModelAlgorithmAssociations' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'collaborationConfiguredModelAlgorithmAssociations', ], 'ListCollaborationMLInputChannels' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'collaborationMLInputChannelsList', ], 'ListCollaborationTrainedModelExportJobs' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'collaborationTrainedModelExportJobs', ], 'ListCollaborationTrainedModelInferenceJobs' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'collaborationTrainedModelInferenceJobs', ], 'ListCollaborationTrainedModels' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'collaborationTrainedModels', ], 'ListConfiguredAudienceModels' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'configuredAudienceModels', ], 'ListConfiguredModelAlgorithmAssociations' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'configuredModelAlgorithmAssociations', ], 'ListConfiguredModelAlgorithms' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'configuredModelAlgorithms', ], 'ListMLInputChannels' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'mlInputChannelsList', ], 'ListTrainedModelInferenceJobs' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'trainedModelInferenceJobs', ], 'ListTrainedModelVersions' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'trainedModels', ], 'ListTrainedModels' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'trainedModels', ], 'ListTrainingDatasets' => [ 'input_token' => 'nextToken', 'output_token' => 'nextToken', 'limit_key' => 'maxResults', 'result_key' => 'trainingDatasets', ], ],];
