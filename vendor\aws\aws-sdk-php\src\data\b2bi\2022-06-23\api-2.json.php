<?php
// This file was auto-generated from sdk-root/src/data/b2bi/2022-06-23/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2022-06-23', 'auth' => [ 'aws.auth#sigv4', ], 'endpointPrefix' => 'b2bi', 'jsonVersion' => '1.0', 'protocol' => 'json', 'protocols' => [ 'json', ], 'serviceAbbreviation' => 'AWS B2BI', 'serviceFullName' => 'AWS B2B Data Interchange', 'serviceId' => 'b2bi', 'signatureVersion' => 'v4', 'signingName' => 'b2bi', 'targetPrefix' => 'B2BI', 'uid' => 'b2bi-2022-06-23', ], 'operations' => [ 'CreateCapability' => [ 'name' => 'CreateCapability', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateCapabilityRequest', ], 'output' => [ 'shape' => 'CreateCapabilityResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'CreatePartnership' => [ 'name' => 'CreatePartnership', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreatePartnershipRequest', ], 'output' => [ 'shape' => 'CreatePartnershipResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'CreateProfile' => [ 'name' => 'CreateProfile', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateProfileRequest', ], 'output' => [ 'shape' => 'CreateProfileResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'CreateStarterMappingTemplate' => [ 'name' => 'CreateStarterMappingTemplate', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateStarterMappingTemplateRequest', ], 'output' => [ 'shape' => 'CreateStarterMappingTemplateResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'CreateTransformer' => [ 'name' => 'CreateTransformer', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'CreateTransformerRequest', ], 'output' => [ 'shape' => 'CreateTransformerResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'DeleteCapability' => [ 'name' => 'DeleteCapability', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteCapabilityRequest', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'DeletePartnership' => [ 'name' => 'DeletePartnership', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeletePartnershipRequest', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'DeleteProfile' => [ 'name' => 'DeleteProfile', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteProfileRequest', ], 'errors' => [ [ 'shape' => 'ConflictException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'DeleteTransformer' => [ 'name' => 'DeleteTransformer', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'DeleteTransformerRequest', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'GenerateMapping' => [ 'name' => 'GenerateMapping', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GenerateMappingRequest', ], 'output' => [ 'shape' => 'GenerateMappingResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'GetCapability' => [ 'name' => 'GetCapability', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetCapabilityRequest', ], 'output' => [ 'shape' => 'GetCapabilityResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetPartnership' => [ 'name' => 'GetPartnership', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetPartnershipRequest', ], 'output' => [ 'shape' => 'GetPartnershipResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetProfile' => [ 'name' => 'GetProfile', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetProfileRequest', ], 'output' => [ 'shape' => 'GetProfileResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetTransformer' => [ 'name' => 'GetTransformer', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetTransformerRequest', ], 'output' => [ 'shape' => 'GetTransformerResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'GetTransformerJob' => [ 'name' => 'GetTransformerJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'GetTransformerJobRequest', ], 'output' => [ 'shape' => 'GetTransformerJobResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListCapabilities' => [ 'name' => 'ListCapabilities', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListCapabilitiesRequest', ], 'output' => [ 'shape' => 'ListCapabilitiesResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListPartnerships' => [ 'name' => 'ListPartnerships', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListPartnershipsRequest', ], 'output' => [ 'shape' => 'ListPartnershipsResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListProfiles' => [ 'name' => 'ListProfiles', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListProfilesRequest', ], 'output' => [ 'shape' => 'ListProfilesResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListTagsForResource' => [ 'name' => 'ListTagsForResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTagsForResourceRequest', ], 'output' => [ 'shape' => 'ListTagsForResourceResponse', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], ], 'ListTransformers' => [ 'name' => 'ListTransformers', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'ListTransformersRequest', ], 'output' => [ 'shape' => 'ListTransformersResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'StartTransformerJob' => [ 'name' => 'StartTransformerJob', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'StartTransformerJobRequest', ], 'output' => [ 'shape' => 'StartTransformerJobResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'TagResource' => [ 'name' => 'TagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TagResourceRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'InternalServerException', ], ], ], 'TestConversion' => [ 'name' => 'TestConversion', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TestConversionRequest', ], 'output' => [ 'shape' => 'TestConversionResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'TestMapping' => [ 'name' => 'TestMapping', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TestMappingRequest', ], 'output' => [ 'shape' => 'TestMappingResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'TestParsing' => [ 'name' => 'TestParsing', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'TestParsingRequest', ], 'output' => [ 'shape' => 'TestParsingResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'UntagResource' => [ 'name' => 'UntagResource', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UntagResourceRequest', ], 'errors' => [ [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'UpdateCapability' => [ 'name' => 'UpdateCapability', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateCapabilityRequest', ], 'output' => [ 'shape' => 'UpdateCapabilityResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'UpdatePartnership' => [ 'name' => 'UpdatePartnership', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdatePartnershipRequest', ], 'output' => [ 'shape' => 'UpdatePartnershipResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'UpdateProfile' => [ 'name' => 'UpdateProfile', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateProfileRequest', ], 'output' => [ 'shape' => 'UpdateProfileResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], 'UpdateTransformer' => [ 'name' => 'UpdateTransformer', 'http' => [ 'method' => 'POST', 'requestUri' => '/', ], 'input' => [ 'shape' => 'UpdateTransformerRequest', ], 'output' => [ 'shape' => 'UpdateTransformerResponse', ], 'errors' => [ [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ValidationException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'ServiceQuotaExceededException', ], [ 'shape' => 'InternalServerException', ], ], 'idempotent' => true, ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'AdvancedOptions' => [ 'type' => 'structure', 'members' => [ 'x12' => [ 'shape' => 'X12AdvancedOptions', ], ], ], 'AmazonResourceName' => [ 'type' => 'string', 'max' => 1011, 'min' => 1, ], 'BucketName' => [ 'type' => 'string', 'max' => 63, 'min' => 3, ], 'BusinessName' => [ 'type' => 'string', 'max' => 254, 'min' => 1, ], 'CapabilityConfiguration' => [ 'type' => 'structure', 'members' => [ 'edi' => [ 'shape' => 'EdiConfiguration', ], ], 'union' => true, ], 'CapabilityDirection' => [ 'type' => 'string', 'enum' => [ 'INBOUND', 'OUTBOUND', ], ], 'CapabilityId' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[a-zA-Z0-9_-]+', ], 'CapabilityList' => [ 'type' => 'list', 'member' => [ 'shape' => 'CapabilitySummary', ], ], 'CapabilityName' => [ 'type' => 'string', 'max' => 254, 'min' => 1, ], 'CapabilityOptions' => [ 'type' => 'structure', 'members' => [ 'outboundEdi' => [ 'shape' => 'OutboundEdiOptions', ], 'inboundEdi' => [ 'shape' => 'InboundEdiOptions', ], ], ], 'CapabilitySummary' => [ 'type' => 'structure', 'required' => [ 'capabilityId', 'name', 'type', 'createdAt', ], 'members' => [ 'capabilityId' => [ 'shape' => 'CapabilityId', ], 'name' => [ 'shape' => 'CapabilityName', ], 'type' => [ 'shape' => 'CapabilityType', ], 'createdAt' => [ 'shape' => 'CreatedDate', ], 'modifiedAt' => [ 'shape' => 'ModifiedDate', ], ], ], 'CapabilityType' => [ 'type' => 'string', 'enum' => [ 'edi', ], ], 'ConflictException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'ConversionSource' => [ 'type' => 'structure', 'required' => [ 'fileFormat', 'inputFile', ], 'members' => [ 'fileFormat' => [ 'shape' => 'ConversionSourceFormat', ], 'inputFile' => [ 'shape' => 'InputFileSource', ], ], ], 'ConversionSourceFormat' => [ 'type' => 'string', 'enum' => [ 'JSON', 'XML', ], ], 'ConversionTarget' => [ 'type' => 'structure', 'required' => [ 'fileFormat', ], 'members' => [ 'fileFormat' => [ 'shape' => 'ConversionTargetFormat', ], 'formatDetails' => [ 'shape' => 'ConversionTargetFormatDetails', ], 'outputSampleFile' => [ 'shape' => 'OutputSampleFileSource', ], ], ], 'ConversionTargetFormat' => [ 'type' => 'string', 'enum' => [ 'X12', ], ], 'ConversionTargetFormatDetails' => [ 'type' => 'structure', 'members' => [ 'x12' => [ 'shape' => 'X12Details', ], ], 'union' => true, ], 'CreateCapabilityRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'type', 'configuration', ], 'members' => [ 'name' => [ 'shape' => 'CapabilityName', ], 'type' => [ 'shape' => 'CapabilityType', ], 'configuration' => [ 'shape' => 'CapabilityConfiguration', ], 'instructionsDocuments' => [ 'shape' => 'InstructionsDocuments', ], 'clientToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'CreateCapabilityResponse' => [ 'type' => 'structure', 'required' => [ 'capabilityId', 'capabilityArn', 'name', 'type', 'configuration', 'createdAt', ], 'members' => [ 'capabilityId' => [ 'shape' => 'CapabilityId', ], 'capabilityArn' => [ 'shape' => 'ResourceArn', ], 'name' => [ 'shape' => 'CapabilityName', ], 'type' => [ 'shape' => 'CapabilityType', ], 'configuration' => [ 'shape' => 'CapabilityConfiguration', ], 'instructionsDocuments' => [ 'shape' => 'InstructionsDocuments', ], 'createdAt' => [ 'shape' => 'CreatedDate', ], ], ], 'CreatePartnershipRequest' => [ 'type' => 'structure', 'required' => [ 'profileId', 'name', 'email', 'capabilities', ], 'members' => [ 'profileId' => [ 'shape' => 'ProfileId', ], 'name' => [ 'shape' => 'PartnerName', ], 'email' => [ 'shape' => 'Email', ], 'phone' => [ 'shape' => 'Phone', ], 'capabilities' => [ 'shape' => 'PartnershipCapabilities', ], 'capabilityOptions' => [ 'shape' => 'CapabilityOptions', ], 'clientToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'CreatePartnershipResponse' => [ 'type' => 'structure', 'required' => [ 'profileId', 'partnershipId', 'partnershipArn', 'createdAt', ], 'members' => [ 'profileId' => [ 'shape' => 'ProfileId', ], 'partnershipId' => [ 'shape' => 'PartnershipId', ], 'partnershipArn' => [ 'shape' => 'ResourceArn', ], 'name' => [ 'shape' => 'PartnerName', ], 'email' => [ 'shape' => 'Email', ], 'phone' => [ 'shape' => 'Phone', ], 'capabilities' => [ 'shape' => 'PartnershipCapabilities', ], 'capabilityOptions' => [ 'shape' => 'CapabilityOptions', ], 'tradingPartnerId' => [ 'shape' => 'TradingPartnerId', ], 'createdAt' => [ 'shape' => 'CreatedDate', ], ], ], 'CreateProfileRequest' => [ 'type' => 'structure', 'required' => [ 'name', 'phone', 'businessName', 'logging', ], 'members' => [ 'name' => [ 'shape' => 'ProfileName', ], 'email' => [ 'shape' => 'Email', ], 'phone' => [ 'shape' => 'Phone', ], 'businessName' => [ 'shape' => 'BusinessName', ], 'logging' => [ 'shape' => 'Logging', ], 'clientToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], 'tags' => [ 'shape' => 'TagList', ], ], ], 'CreateProfileResponse' => [ 'type' => 'structure', 'required' => [ 'profileId', 'profileArn', 'name', 'businessName', 'phone', 'createdAt', ], 'members' => [ 'profileId' => [ 'shape' => 'ProfileId', ], 'profileArn' => [ 'shape' => 'ResourceArn', ], 'name' => [ 'shape' => 'ProfileName', ], 'businessName' => [ 'shape' => 'BusinessName', ], 'phone' => [ 'shape' => 'Phone', ], 'email' => [ 'shape' => 'Email', ], 'logging' => [ 'shape' => 'Logging', ], 'logGroupName' => [ 'shape' => 'LogGroupName', ], 'createdAt' => [ 'shape' => 'CreatedDate', ], ], ], 'CreateStarterMappingTemplateRequest' => [ 'type' => 'structure', 'required' => [ 'mappingType', 'templateDetails', ], 'members' => [ 'outputSampleLocation' => [ 'shape' => 'S3Location', ], 'mappingType' => [ 'shape' => 'MappingType', ], 'templateDetails' => [ 'shape' => 'TemplateDetails', ], ], ], 'CreateStarterMappingTemplateResponse' => [ 'type' => 'structure', 'required' => [ 'mappingTemplate', ], 'members' => [ 'mappingTemplate' => [ 'shape' => 'String', ], ], ], 'CreateTransformerRequest' => [ 'type' => 'structure', 'required' => [ 'name', ], 'members' => [ 'name' => [ 'shape' => 'TransformerName', ], 'clientToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], 'tags' => [ 'shape' => 'TagList', ], 'fileFormat' => [ 'shape' => 'FileFormat', 'deprecated' => true, 'deprecatedMessage' => 'This is a legacy trait. Please use input-conversion or output-conversion.', ], 'mappingTemplate' => [ 'shape' => 'MappingTemplate', 'deprecated' => true, 'deprecatedMessage' => 'This is a legacy trait. Please use input-conversion or output-conversion.', ], 'ediType' => [ 'shape' => 'EdiType', 'deprecated' => true, 'deprecatedMessage' => 'This is a legacy trait. Please use input-conversion or output-conversion.', ], 'sampleDocument' => [ 'shape' => 'FileLocation', 'deprecated' => true, 'deprecatedMessage' => 'This is a legacy trait. Please use input-conversion or output-conversion.', ], 'inputConversion' => [ 'shape' => 'InputConversion', ], 'mapping' => [ 'shape' => 'Mapping', ], 'outputConversion' => [ 'shape' => 'OutputConversion', ], 'sampleDocuments' => [ 'shape' => 'SampleDocuments', ], ], ], 'CreateTransformerResponse' => [ 'type' => 'structure', 'required' => [ 'transformerId', 'transformerArn', 'name', 'status', 'createdAt', ], 'members' => [ 'transformerId' => [ 'shape' => 'TransformerId', ], 'transformerArn' => [ 'shape' => 'ResourceArn', ], 'name' => [ 'shape' => 'TransformerName', ], 'status' => [ 'shape' => 'TransformerStatus', ], 'createdAt' => [ 'shape' => 'CreatedDate', ], 'fileFormat' => [ 'shape' => 'FileFormat', 'deprecated' => true, 'deprecatedMessage' => 'This is a legacy trait. Please use input-conversion or output-conversion.', ], 'mappingTemplate' => [ 'shape' => 'MappingTemplate', 'deprecated' => true, 'deprecatedMessage' => 'This is a legacy trait. Please use input-conversion or output-conversion.', ], 'ediType' => [ 'shape' => 'EdiType', 'deprecated' => true, 'deprecatedMessage' => 'This is a legacy trait. Please use input-conversion or output-conversion.', ], 'sampleDocument' => [ 'shape' => 'FileLocation', 'deprecated' => true, 'deprecatedMessage' => 'This is a legacy trait. Please use input-conversion or output-conversion.', ], 'inputConversion' => [ 'shape' => 'InputConversion', ], 'mapping' => [ 'shape' => 'Mapping', ], 'outputConversion' => [ 'shape' => 'OutputConversion', ], 'sampleDocuments' => [ 'shape' => 'SampleDocuments', ], ], ], 'CreatedDate' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'DeleteCapabilityRequest' => [ 'type' => 'structure', 'required' => [ 'capabilityId', ], 'members' => [ 'capabilityId' => [ 'shape' => 'CapabilityId', ], ], ], 'DeletePartnershipRequest' => [ 'type' => 'structure', 'required' => [ 'partnershipId', ], 'members' => [ 'partnershipId' => [ 'shape' => 'PartnershipId', ], ], ], 'DeleteProfileRequest' => [ 'type' => 'structure', 'required' => [ 'profileId', ], 'members' => [ 'profileId' => [ 'shape' => 'ProfileId', ], ], ], 'DeleteTransformerRequest' => [ 'type' => 'structure', 'required' => [ 'transformerId', ], 'members' => [ 'transformerId' => [ 'shape' => 'TransformerId', ], ], ], 'EdiConfiguration' => [ 'type' => 'structure', 'required' => [ 'type', 'inputLocation', 'outputLocation', 'transformerId', ], 'members' => [ 'capabilityDirection' => [ 'shape' => 'CapabilityDirection', ], 'type' => [ 'shape' => 'EdiType', ], 'inputLocation' => [ 'shape' => 'S3Location', ], 'outputLocation' => [ 'shape' => 'S3Location', ], 'transformerId' => [ 'shape' => 'TransformerId', ], ], ], 'EdiType' => [ 'type' => 'structure', 'members' => [ 'x12Details' => [ 'shape' => 'X12Details', ], ], 'union' => true, ], 'Email' => [ 'type' => 'string', 'max' => 254, 'min' => 5, 'pattern' => '[\\w\\.\\-]+@[\\w\\.\\-]+', 'sensitive' => true, ], 'ErrorMessage' => [ 'type' => 'string', 'max' => 1024, 'min' => 10, ], 'FileFormat' => [ 'type' => 'string', 'enum' => [ 'XML', 'JSON', 'NOT_USED', ], ], 'FileLocation' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, ], 'FormatOptions' => [ 'type' => 'structure', 'members' => [ 'x12' => [ 'shape' => 'X12Details', ], ], 'union' => true, ], 'FromFormat' => [ 'type' => 'string', 'enum' => [ 'X12', ], ], 'GenerateMappingInputFileContent' => [ 'type' => 'string', 'max' => 5000000, 'min' => 0, ], 'GenerateMappingOutputFileContent' => [ 'type' => 'string', 'max' => 5000000, 'min' => 0, ], 'GenerateMappingRequest' => [ 'type' => 'structure', 'required' => [ 'inputFileContent', 'outputFileContent', 'mappingType', ], 'members' => [ 'inputFileContent' => [ 'shape' => 'GenerateMappingInputFileContent', ], 'outputFileContent' => [ 'shape' => 'GenerateMappingOutputFileContent', ], 'mappingType' => [ 'shape' => 'MappingType', ], ], ], 'GenerateMappingResponse' => [ 'type' => 'structure', 'required' => [ 'mappingTemplate', ], 'members' => [ 'mappingTemplate' => [ 'shape' => 'String', ], 'mappingAccuracy' => [ 'shape' => 'GenerateMappingResponseMappingAccuracyFloat', ], ], ], 'GenerateMappingResponseMappingAccuracyFloat' => [ 'type' => 'float', 'box' => true, 'max' => 1.0, 'min' => 0.0, ], 'GetCapabilityRequest' => [ 'type' => 'structure', 'required' => [ 'capabilityId', ], 'members' => [ 'capabilityId' => [ 'shape' => 'CapabilityId', ], ], ], 'GetCapabilityResponse' => [ 'type' => 'structure', 'required' => [ 'capabilityId', 'capabilityArn', 'name', 'type', 'configuration', 'createdAt', ], 'members' => [ 'capabilityId' => [ 'shape' => 'CapabilityId', ], 'capabilityArn' => [ 'shape' => 'ResourceArn', ], 'name' => [ 'shape' => 'CapabilityName', ], 'type' => [ 'shape' => 'CapabilityType', ], 'configuration' => [ 'shape' => 'CapabilityConfiguration', ], 'instructionsDocuments' => [ 'shape' => 'InstructionsDocuments', ], 'createdAt' => [ 'shape' => 'CreatedDate', ], 'modifiedAt' => [ 'shape' => 'ModifiedDate', ], ], ], 'GetPartnershipRequest' => [ 'type' => 'structure', 'required' => [ 'partnershipId', ], 'members' => [ 'partnershipId' => [ 'shape' => 'PartnershipId', ], ], ], 'GetPartnershipResponse' => [ 'type' => 'structure', 'required' => [ 'profileId', 'partnershipId', 'partnershipArn', 'createdAt', ], 'members' => [ 'profileId' => [ 'shape' => 'ProfileId', ], 'partnershipId' => [ 'shape' => 'PartnershipId', ], 'partnershipArn' => [ 'shape' => 'ResourceArn', ], 'name' => [ 'shape' => 'PartnerName', ], 'email' => [ 'shape' => 'Email', ], 'phone' => [ 'shape' => 'Phone', ], 'capabilities' => [ 'shape' => 'PartnershipCapabilities', ], 'capabilityOptions' => [ 'shape' => 'CapabilityOptions', ], 'tradingPartnerId' => [ 'shape' => 'TradingPartnerId', ], 'createdAt' => [ 'shape' => 'CreatedDate', ], 'modifiedAt' => [ 'shape' => 'ModifiedDate', ], ], ], 'GetProfileRequest' => [ 'type' => 'structure', 'required' => [ 'profileId', ], 'members' => [ 'profileId' => [ 'shape' => 'ProfileId', ], ], ], 'GetProfileResponse' => [ 'type' => 'structure', 'required' => [ 'profileId', 'profileArn', 'name', 'phone', 'businessName', 'createdAt', ], 'members' => [ 'profileId' => [ 'shape' => 'ProfileId', ], 'profileArn' => [ 'shape' => 'ResourceArn', ], 'name' => [ 'shape' => 'ProfileName', ], 'email' => [ 'shape' => 'Email', ], 'phone' => [ 'shape' => 'Phone', ], 'businessName' => [ 'shape' => 'BusinessName', ], 'logging' => [ 'shape' => 'Logging', ], 'logGroupName' => [ 'shape' => 'LogGroupName', ], 'createdAt' => [ 'shape' => 'CreatedDate', ], 'modifiedAt' => [ 'shape' => 'ModifiedDate', ], ], ], 'GetTransformerJobRequest' => [ 'type' => 'structure', 'required' => [ 'transformerJobId', 'transformerId', ], 'members' => [ 'transformerJobId' => [ 'shape' => 'TransformerJobId', ], 'transformerId' => [ 'shape' => 'TransformerId', ], ], ], 'GetTransformerJobResponse' => [ 'type' => 'structure', 'required' => [ 'status', ], 'members' => [ 'status' => [ 'shape' => 'TransformerJobStatus', ], 'outputFiles' => [ 'shape' => 'S3LocationList', ], 'message' => [ 'shape' => 'String', ], ], ], 'GetTransformerRequest' => [ 'type' => 'structure', 'required' => [ 'transformerId', ], 'members' => [ 'transformerId' => [ 'shape' => 'TransformerId', ], ], ], 'GetTransformerResponse' => [ 'type' => 'structure', 'required' => [ 'transformerId', 'transformerArn', 'name', 'status', 'createdAt', ], 'members' => [ 'transformerId' => [ 'shape' => 'TransformerId', ], 'transformerArn' => [ 'shape' => 'ResourceArn', ], 'name' => [ 'shape' => 'TransformerName', ], 'status' => [ 'shape' => 'TransformerStatus', ], 'createdAt' => [ 'shape' => 'CreatedDate', ], 'modifiedAt' => [ 'shape' => 'ModifiedDate', ], 'fileFormat' => [ 'shape' => 'FileFormat', 'deprecated' => true, 'deprecatedMessage' => 'This is a legacy trait. Please use input-conversion or output-conversion.', ], 'mappingTemplate' => [ 'shape' => 'MappingTemplate', 'deprecated' => true, 'deprecatedMessage' => 'This is a legacy trait. Please use input-conversion or output-conversion.', ], 'ediType' => [ 'shape' => 'EdiType', 'deprecated' => true, 'deprecatedMessage' => 'This is a legacy trait. Please use input-conversion or output-conversion.', ], 'sampleDocument' => [ 'shape' => 'FileLocation', 'deprecated' => true, 'deprecatedMessage' => 'This is a legacy trait. Please use input-conversion or output-conversion.', ], 'inputConversion' => [ 'shape' => 'InputConversion', ], 'mapping' => [ 'shape' => 'Mapping', ], 'outputConversion' => [ 'shape' => 'OutputConversion', ], 'sampleDocuments' => [ 'shape' => 'SampleDocuments', ], ], ], 'InboundEdiOptions' => [ 'type' => 'structure', 'members' => [ 'x12' => [ 'shape' => 'X12InboundEdiOptions', ], ], ], 'InputConversion' => [ 'type' => 'structure', 'required' => [ 'fromFormat', ], 'members' => [ 'fromFormat' => [ 'shape' => 'FromFormat', ], 'formatOptions' => [ 'shape' => 'FormatOptions', ], 'advancedOptions' => [ 'shape' => 'AdvancedOptions', ], ], ], 'InputFileSource' => [ 'type' => 'structure', 'members' => [ 'fileContent' => [ 'shape' => 'InputFileSourceFileContentString', ], ], 'union' => true, ], 'InputFileSourceFileContentString' => [ 'type' => 'string', 'max' => 5000000, 'min' => 1, ], 'InstructionsDocuments' => [ 'type' => 'list', 'member' => [ 'shape' => 'S3Location', ], 'max' => 5, 'min' => 0, ], 'Integer' => [ 'type' => 'integer', 'box' => true, ], 'InternalServerException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], 'retryAfterSeconds' => [ 'shape' => 'Integer', ], ], 'exception' => true, 'fault' => true, 'retryable' => [ 'throttling' => false, ], ], 'KeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'SampleDocumentKeys', ], ], 'LineLength' => [ 'type' => 'integer', 'box' => true, 'min' => 1, ], 'LineTerminator' => [ 'type' => 'string', 'enum' => [ 'CRLF', 'LF', 'CR', ], ], 'ListCapabilitiesRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'PageToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListCapabilitiesResponse' => [ 'type' => 'structure', 'required' => [ 'capabilities', ], 'members' => [ 'capabilities' => [ 'shape' => 'CapabilityList', ], 'nextToken' => [ 'shape' => 'PageToken', ], ], ], 'ListPartnershipsRequest' => [ 'type' => 'structure', 'members' => [ 'profileId' => [ 'shape' => 'ProfileId', ], 'nextToken' => [ 'shape' => 'PageToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListPartnershipsResponse' => [ 'type' => 'structure', 'required' => [ 'partnerships', ], 'members' => [ 'partnerships' => [ 'shape' => 'PartnershipList', ], 'nextToken' => [ 'shape' => 'PageToken', ], ], ], 'ListProfilesRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'PageToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListProfilesResponse' => [ 'type' => 'structure', 'required' => [ 'profiles', ], 'members' => [ 'profiles' => [ 'shape' => 'ProfileList', ], 'nextToken' => [ 'shape' => 'PageToken', ], ], ], 'ListTagsForResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceARN', ], 'members' => [ 'ResourceARN' => [ 'shape' => 'AmazonResourceName', ], ], ], 'ListTagsForResourceResponse' => [ 'type' => 'structure', 'members' => [ 'Tags' => [ 'shape' => 'TagList', ], ], ], 'ListTransformersRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'PageToken', ], 'maxResults' => [ 'shape' => 'MaxResults', ], ], ], 'ListTransformersResponse' => [ 'type' => 'structure', 'required' => [ 'transformers', ], 'members' => [ 'transformers' => [ 'shape' => 'TransformerList', ], 'nextToken' => [ 'shape' => 'PageToken', ], ], ], 'LogGroupName' => [ 'type' => 'string', 'max' => 512, 'min' => 1, ], 'Logging' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'Mapping' => [ 'type' => 'structure', 'required' => [ 'templateLanguage', ], 'members' => [ 'templateLanguage' => [ 'shape' => 'MappingTemplateLanguage', ], 'template' => [ 'shape' => 'MappingTemplate', ], ], ], 'MappingTemplate' => [ 'type' => 'string', 'max' => 350000, 'min' => 0, ], 'MappingTemplateLanguage' => [ 'type' => 'string', 'enum' => [ 'XSLT', 'JSONATA', ], ], 'MappingType' => [ 'type' => 'string', 'enum' => [ 'JSONATA', 'XSLT', ], ], 'MaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'ModifiedDate' => [ 'type' => 'timestamp', 'timestampFormat' => 'iso8601', ], 'OutboundEdiOptions' => [ 'type' => 'structure', 'members' => [ 'x12' => [ 'shape' => 'X12Envelope', ], ], 'union' => true, ], 'OutputConversion' => [ 'type' => 'structure', 'required' => [ 'toFormat', ], 'members' => [ 'toFormat' => [ 'shape' => 'ToFormat', ], 'formatOptions' => [ 'shape' => 'FormatOptions', ], ], ], 'OutputSampleFileSource' => [ 'type' => 'structure', 'members' => [ 'fileLocation' => [ 'shape' => 'S3Location', ], ], 'union' => true, ], 'PageToken' => [ 'type' => 'string', 'max' => 2048, 'min' => 1, ], 'ParsedSplitFileContentsList' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'PartnerName' => [ 'type' => 'string', 'max' => 254, 'min' => 1, ], 'PartnershipCapabilities' => [ 'type' => 'list', 'member' => [ 'shape' => 'CapabilityId', ], ], 'PartnershipId' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[a-zA-Z0-9_-]+', ], 'PartnershipList' => [ 'type' => 'list', 'member' => [ 'shape' => 'PartnershipSummary', ], ], 'PartnershipSummary' => [ 'type' => 'structure', 'required' => [ 'profileId', 'partnershipId', 'createdAt', ], 'members' => [ 'profileId' => [ 'shape' => 'ProfileId', ], 'partnershipId' => [ 'shape' => 'PartnershipId', ], 'name' => [ 'shape' => 'PartnerName', ], 'capabilities' => [ 'shape' => 'PartnershipCapabilities', ], 'capabilityOptions' => [ 'shape' => 'CapabilityOptions', ], 'tradingPartnerId' => [ 'shape' => 'TradingPartnerId', ], 'createdAt' => [ 'shape' => 'CreatedDate', ], 'modifiedAt' => [ 'shape' => 'ModifiedDate', ], ], ], 'Phone' => [ 'type' => 'string', 'max' => 22, 'min' => 7, 'pattern' => '\\+?([0-9 \\t\\-()\\/]{7,})(?:\\s*(?:#|x\\.?|ext\\.?|extension) \\t*(\\d+))?', 'sensitive' => true, ], 'ProfileId' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[a-zA-Z0-9_-]+', ], 'ProfileList' => [ 'type' => 'list', 'member' => [ 'shape' => 'ProfileSummary', ], ], 'ProfileName' => [ 'type' => 'string', 'max' => 254, 'min' => 1, ], 'ProfileSummary' => [ 'type' => 'structure', 'required' => [ 'profileId', 'name', 'businessName', 'createdAt', ], 'members' => [ 'profileId' => [ 'shape' => 'ProfileId', ], 'name' => [ 'shape' => 'ProfileName', ], 'businessName' => [ 'shape' => 'BusinessName', ], 'logging' => [ 'shape' => 'Logging', ], 'logGroupName' => [ 'shape' => 'LogGroupName', ], 'createdAt' => [ 'shape' => 'CreatedDate', ], 'modifiedAt' => [ 'shape' => 'ModifiedDate', ], ], ], 'ResourceArn' => [ 'type' => 'string', 'max' => 255, 'min' => 1, ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'S3Key' => [ 'type' => 'string', 'max' => 1024, 'min' => 0, ], 'S3Location' => [ 'type' => 'structure', 'members' => [ 'bucketName' => [ 'shape' => 'BucketName', ], 'key' => [ 'shape' => 'S3Key', ], ], ], 'S3LocationList' => [ 'type' => 'list', 'member' => [ 'shape' => 'S3Location', ], ], 'SampleDocumentKeys' => [ 'type' => 'structure', 'members' => [ 'input' => [ 'shape' => 'S3Key', ], 'output' => [ 'shape' => 'S3Key', ], ], ], 'SampleDocuments' => [ 'type' => 'structure', 'required' => [ 'bucketName', 'keys', ], 'members' => [ 'bucketName' => [ 'shape' => 'BucketName', ], 'keys' => [ 'shape' => 'KeyList', ], ], ], 'ServiceQuotaExceededException' => [ 'type' => 'structure', 'required' => [ 'message', 'resourceId', 'resourceType', 'serviceCode', 'quotaCode', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], 'resourceId' => [ 'shape' => 'String', ], 'resourceType' => [ 'shape' => 'String', ], 'serviceCode' => [ 'shape' => 'String', ], 'quotaCode' => [ 'shape' => 'String', ], ], 'exception' => true, ], 'StartTransformerJobRequest' => [ 'type' => 'structure', 'required' => [ 'inputFile', 'outputLocation', 'transformerId', ], 'members' => [ 'inputFile' => [ 'shape' => 'S3Location', ], 'outputLocation' => [ 'shape' => 'S3Location', ], 'transformerId' => [ 'shape' => 'TransformerId', ], 'clientToken' => [ 'shape' => 'String', 'idempotencyToken' => true, ], ], ], 'StartTransformerJobResponse' => [ 'type' => 'structure', 'required' => [ 'transformerJobId', ], 'members' => [ 'transformerJobId' => [ 'shape' => 'TransformerJobId', ], ], ], 'StartingFunctionalGroupControlNumber' => [ 'type' => 'integer', 'box' => true, 'max' => 999999999, 'min' => 1, ], 'StartingInterchangeControlNumber' => [ 'type' => 'integer', 'box' => true, 'max' => 999999999, 'min' => 1, ], 'StartingTransactionSetControlNumber' => [ 'type' => 'integer', 'box' => true, 'max' => 999999999, 'min' => 1, ], 'String' => [ 'type' => 'string', ], 'Tag' => [ 'type' => 'structure', 'required' => [ 'Key', 'Value', ], 'members' => [ 'Key' => [ 'shape' => 'TagKey', ], 'Value' => [ 'shape' => 'TagValue', ], ], ], 'TagKey' => [ 'type' => 'string', 'max' => 128, 'min' => 1, ], 'TagKeyList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TagKey', ], 'max' => 200, 'min' => 0, ], 'TagList' => [ 'type' => 'list', 'member' => [ 'shape' => 'Tag', ], 'max' => 200, 'min' => 0, ], 'TagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceARN', 'Tags', ], 'members' => [ 'ResourceARN' => [ 'shape' => 'AmazonResourceName', ], 'Tags' => [ 'shape' => 'TagList', ], ], ], 'TagValue' => [ 'type' => 'string', 'max' => 256, 'min' => 0, ], 'TemplateDetails' => [ 'type' => 'structure', 'members' => [ 'x12' => [ 'shape' => 'X12Details', ], ], 'union' => true, ], 'TestConversionRequest' => [ 'type' => 'structure', 'required' => [ 'source', 'target', ], 'members' => [ 'source' => [ 'shape' => 'ConversionSource', ], 'target' => [ 'shape' => 'ConversionTarget', ], ], ], 'TestConversionResponse' => [ 'type' => 'structure', 'required' => [ 'convertedFileContent', ], 'members' => [ 'convertedFileContent' => [ 'shape' => 'String', ], 'validationMessages' => [ 'shape' => 'ValidationMessages', ], ], ], 'TestMappingInputFileContent' => [ 'type' => 'string', 'max' => 5000000, 'min' => 0, ], 'TestMappingRequest' => [ 'type' => 'structure', 'required' => [ 'inputFileContent', 'mappingTemplate', 'fileFormat', ], 'members' => [ 'inputFileContent' => [ 'shape' => 'TestMappingInputFileContent', ], 'mappingTemplate' => [ 'shape' => 'MappingTemplate', ], 'fileFormat' => [ 'shape' => 'FileFormat', ], ], ], 'TestMappingResponse' => [ 'type' => 'structure', 'required' => [ 'mappedFileContent', ], 'members' => [ 'mappedFileContent' => [ 'shape' => 'String', ], ], ], 'TestParsingRequest' => [ 'type' => 'structure', 'required' => [ 'inputFile', 'fileFormat', 'ediType', ], 'members' => [ 'inputFile' => [ 'shape' => 'S3Location', ], 'fileFormat' => [ 'shape' => 'FileFormat', ], 'ediType' => [ 'shape' => 'EdiType', ], 'advancedOptions' => [ 'shape' => 'AdvancedOptions', ], ], ], 'TestParsingResponse' => [ 'type' => 'structure', 'required' => [ 'parsedFileContent', ], 'members' => [ 'parsedFileContent' => [ 'shape' => 'String', ], 'parsedSplitFileContents' => [ 'shape' => 'ParsedSplitFileContentsList', ], ], ], 'ThrottlingException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'ErrorMessage', ], 'retryAfterSeconds' => [ 'shape' => 'Integer', ], ], 'exception' => true, 'retryable' => [ 'throttling' => false, ], ], 'ToFormat' => [ 'type' => 'string', 'enum' => [ 'X12', ], ], 'TradingPartnerId' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[a-zA-Z0-9_-]+', ], 'TransformerId' => [ 'type' => 'string', 'max' => 64, 'min' => 1, 'pattern' => '[a-zA-Z0-9_-]+', ], 'TransformerJobId' => [ 'type' => 'string', 'max' => 25, 'min' => 25, 'pattern' => '[a-zA-Z0-9_-]+', ], 'TransformerJobStatus' => [ 'type' => 'string', 'enum' => [ 'running', 'succeeded', 'failed', ], ], 'TransformerList' => [ 'type' => 'list', 'member' => [ 'shape' => 'TransformerSummary', ], ], 'TransformerName' => [ 'type' => 'string', 'max' => 254, 'min' => 1, 'pattern' => '[a-zA-Z0-9_-]{1,512}', ], 'TransformerStatus' => [ 'type' => 'string', 'enum' => [ 'active', 'inactive', ], ], 'TransformerSummary' => [ 'type' => 'structure', 'required' => [ 'transformerId', 'name', 'status', 'createdAt', ], 'members' => [ 'transformerId' => [ 'shape' => 'TransformerId', ], 'name' => [ 'shape' => 'TransformerName', ], 'status' => [ 'shape' => 'TransformerStatus', ], 'createdAt' => [ 'shape' => 'CreatedDate', ], 'modifiedAt' => [ 'shape' => 'ModifiedDate', ], 'fileFormat' => [ 'shape' => 'FileFormat', 'deprecated' => true, 'deprecatedMessage' => 'This is a legacy trait. Please use input-conversion or output-conversion.', ], 'mappingTemplate' => [ 'shape' => 'MappingTemplate', 'deprecated' => true, 'deprecatedMessage' => 'This is a legacy trait. Please use input-conversion or output-conversion.', ], 'ediType' => [ 'shape' => 'EdiType', 'deprecated' => true, 'deprecatedMessage' => 'This is a legacy trait. Please use input-conversion or output-conversion.', ], 'sampleDocument' => [ 'shape' => 'FileLocation', 'deprecated' => true, 'deprecatedMessage' => 'This is a legacy trait. Please use input-conversion or output-conversion.', ], 'inputConversion' => [ 'shape' => 'InputConversion', ], 'mapping' => [ 'shape' => 'Mapping', ], 'outputConversion' => [ 'shape' => 'OutputConversion', ], 'sampleDocuments' => [ 'shape' => 'SampleDocuments', ], ], ], 'UntagResourceRequest' => [ 'type' => 'structure', 'required' => [ 'ResourceARN', 'TagKeys', ], 'members' => [ 'ResourceARN' => [ 'shape' => 'AmazonResourceName', ], 'TagKeys' => [ 'shape' => 'TagKeyList', ], ], ], 'UpdateCapabilityRequest' => [ 'type' => 'structure', 'required' => [ 'capabilityId', ], 'members' => [ 'capabilityId' => [ 'shape' => 'CapabilityId', ], 'name' => [ 'shape' => 'CapabilityName', ], 'configuration' => [ 'shape' => 'CapabilityConfiguration', ], 'instructionsDocuments' => [ 'shape' => 'InstructionsDocuments', ], ], ], 'UpdateCapabilityResponse' => [ 'type' => 'structure', 'required' => [ 'capabilityId', 'capabilityArn', 'name', 'type', 'configuration', 'createdAt', ], 'members' => [ 'capabilityId' => [ 'shape' => 'CapabilityId', ], 'capabilityArn' => [ 'shape' => 'ResourceArn', ], 'name' => [ 'shape' => 'CapabilityName', ], 'type' => [ 'shape' => 'CapabilityType', ], 'configuration' => [ 'shape' => 'CapabilityConfiguration', ], 'instructionsDocuments' => [ 'shape' => 'InstructionsDocuments', ], 'createdAt' => [ 'shape' => 'CreatedDate', ], 'modifiedAt' => [ 'shape' => 'ModifiedDate', ], ], ], 'UpdatePartnershipRequest' => [ 'type' => 'structure', 'required' => [ 'partnershipId', ], 'members' => [ 'partnershipId' => [ 'shape' => 'PartnershipId', ], 'name' => [ 'shape' => 'PartnerName', ], 'capabilities' => [ 'shape' => 'PartnershipCapabilities', ], 'capabilityOptions' => [ 'shape' => 'CapabilityOptions', ], ], ], 'UpdatePartnershipResponse' => [ 'type' => 'structure', 'required' => [ 'profileId', 'partnershipId', 'partnershipArn', 'createdAt', ], 'members' => [ 'profileId' => [ 'shape' => 'ProfileId', ], 'partnershipId' => [ 'shape' => 'PartnershipId', ], 'partnershipArn' => [ 'shape' => 'ResourceArn', ], 'name' => [ 'shape' => 'PartnerName', ], 'email' => [ 'shape' => 'Email', ], 'phone' => [ 'shape' => 'Phone', ], 'capabilities' => [ 'shape' => 'PartnershipCapabilities', ], 'capabilityOptions' => [ 'shape' => 'CapabilityOptions', ], 'tradingPartnerId' => [ 'shape' => 'TradingPartnerId', ], 'createdAt' => [ 'shape' => 'CreatedDate', ], 'modifiedAt' => [ 'shape' => 'ModifiedDate', ], ], ], 'UpdateProfileRequest' => [ 'type' => 'structure', 'required' => [ 'profileId', ], 'members' => [ 'profileId' => [ 'shape' => 'ProfileId', ], 'name' => [ 'shape' => 'ProfileName', ], 'email' => [ 'shape' => 'Email', ], 'phone' => [ 'shape' => 'Phone', ], 'businessName' => [ 'shape' => 'BusinessName', ], ], ], 'UpdateProfileResponse' => [ 'type' => 'structure', 'required' => [ 'profileId', 'profileArn', 'name', 'phone', 'businessName', 'createdAt', ], 'members' => [ 'profileId' => [ 'shape' => 'ProfileId', ], 'profileArn' => [ 'shape' => 'ResourceArn', ], 'name' => [ 'shape' => 'ProfileName', ], 'email' => [ 'shape' => 'Email', ], 'phone' => [ 'shape' => 'Phone', ], 'businessName' => [ 'shape' => 'BusinessName', ], 'logging' => [ 'shape' => 'Logging', ], 'logGroupName' => [ 'shape' => 'LogGroupName', ], 'createdAt' => [ 'shape' => 'CreatedDate', ], 'modifiedAt' => [ 'shape' => 'ModifiedDate', ], ], ], 'UpdateTransformerRequest' => [ 'type' => 'structure', 'required' => [ 'transformerId', ], 'members' => [ 'transformerId' => [ 'shape' => 'TransformerId', ], 'name' => [ 'shape' => 'TransformerName', ], 'status' => [ 'shape' => 'TransformerStatus', ], 'fileFormat' => [ 'shape' => 'FileFormat', 'deprecated' => true, 'deprecatedMessage' => 'This is a legacy trait. Please use input-conversion or output-conversion.', ], 'mappingTemplate' => [ 'shape' => 'MappingTemplate', 'deprecated' => true, 'deprecatedMessage' => 'This is a legacy trait. Please use input-conversion or output-conversion.', ], 'ediType' => [ 'shape' => 'EdiType', 'deprecated' => true, 'deprecatedMessage' => 'This is a legacy trait. Please use input-conversion or output-conversion.', ], 'sampleDocument' => [ 'shape' => 'FileLocation', 'deprecated' => true, 'deprecatedMessage' => 'This is a legacy trait. Please use input-conversion or output-conversion.', ], 'inputConversion' => [ 'shape' => 'InputConversion', ], 'mapping' => [ 'shape' => 'Mapping', ], 'outputConversion' => [ 'shape' => 'OutputConversion', ], 'sampleDocuments' => [ 'shape' => 'SampleDocuments', ], ], ], 'UpdateTransformerResponse' => [ 'type' => 'structure', 'required' => [ 'transformerId', 'transformerArn', 'name', 'status', 'createdAt', 'modifiedAt', ], 'members' => [ 'transformerId' => [ 'shape' => 'TransformerId', ], 'transformerArn' => [ 'shape' => 'ResourceArn', ], 'name' => [ 'shape' => 'TransformerName', ], 'status' => [ 'shape' => 'TransformerStatus', ], 'createdAt' => [ 'shape' => 'CreatedDate', ], 'modifiedAt' => [ 'shape' => 'ModifiedDate', ], 'fileFormat' => [ 'shape' => 'FileFormat', 'deprecated' => true, 'deprecatedMessage' => 'This is a legacy trait. Please use input-conversion or output-conversion.', ], 'mappingTemplate' => [ 'shape' => 'MappingTemplate', 'deprecated' => true, 'deprecatedMessage' => 'This is a legacy trait. Please use input-conversion or output-conversion.', ], 'ediType' => [ 'shape' => 'EdiType', 'deprecated' => true, 'deprecatedMessage' => 'This is a legacy trait. Please use input-conversion or output-conversion.', ], 'sampleDocument' => [ 'shape' => 'FileLocation', 'deprecated' => true, 'deprecatedMessage' => 'This is a legacy trait. Please use input-conversion or output-conversion.', ], 'inputConversion' => [ 'shape' => 'InputConversion', ], 'mapping' => [ 'shape' => 'Mapping', ], 'outputConversion' => [ 'shape' => 'OutputConversion', ], 'sampleDocuments' => [ 'shape' => 'SampleDocuments', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'required' => [ 'Message', ], 'members' => [ 'Message' => [ 'shape' => 'ErrorMessage', ], ], 'exception' => true, ], 'ValidationMessages' => [ 'type' => 'list', 'member' => [ 'shape' => 'String', ], ], 'WrapFormat' => [ 'type' => 'string', 'enum' => [ 'SEGMENT', 'ONE_LINE', 'LINE_LENGTH', ], ], 'WrapOptions' => [ 'type' => 'structure', 'required' => [ 'wrapBy', ], 'members' => [ 'wrapBy' => [ 'shape' => 'WrapFormat', ], 'lineTerminator' => [ 'shape' => 'LineTerminator', ], 'lineLength' => [ 'shape' => 'LineLength', ], ], ], 'X12AcknowledgmentOptions' => [ 'type' => 'structure', 'required' => [ 'functionalAcknowledgment', 'technicalAcknowledgment', ], 'members' => [ 'functionalAcknowledgment' => [ 'shape' => 'X12FunctionalAcknowledgment', ], 'technicalAcknowledgment' => [ 'shape' => 'X12TechnicalAcknowledgment', ], ], ], 'X12AcknowledgmentRequestedCode' => [ 'type' => 'string', 'max' => 1, 'min' => 1, 'pattern' => '[a-zA-Z0-9]*', ], 'X12AdvancedOptions' => [ 'type' => 'structure', 'members' => [ 'splitOptions' => [ 'shape' => 'X12SplitOptions', ], ], ], 'X12ApplicationReceiverCode' => [ 'type' => 'string', 'max' => 15, 'min' => 2, 'pattern' => '[a-zA-Z0-9 ]*', ], 'X12ApplicationSenderCode' => [ 'type' => 'string', 'max' => 15, 'min' => 2, 'pattern' => '[a-zA-Z0-9 ]*', ], 'X12ComponentSeparator' => [ 'type' => 'string', 'max' => 1, 'min' => 1, 'pattern' => '[!&\'()*+,\\-./:;?=%@\\[\\]_{}|<>~^`"]', ], 'X12ControlNumbers' => [ 'type' => 'structure', 'members' => [ 'startingInterchangeControlNumber' => [ 'shape' => 'StartingInterchangeControlNumber', ], 'startingFunctionalGroupControlNumber' => [ 'shape' => 'StartingFunctionalGroupControlNumber', ], 'startingTransactionSetControlNumber' => [ 'shape' => 'StartingTransactionSetControlNumber', ], ], ], 'X12DataElementSeparator' => [ 'type' => 'string', 'max' => 1, 'min' => 1, 'pattern' => '[!&\'()*+,\\-./:;?=%@\\[\\]_{}|<>~^`"]', ], 'X12Delimiters' => [ 'type' => 'structure', 'members' => [ 'componentSeparator' => [ 'shape' => 'X12ComponentSeparator', ], 'dataElementSeparator' => [ 'shape' => 'X12DataElementSeparator', ], 'segmentTerminator' => [ 'shape' => 'X12SegmentTerminator', ], ], ], 'X12Details' => [ 'type' => 'structure', 'members' => [ 'transactionSet' => [ 'shape' => 'X12TransactionSet', ], 'version' => [ 'shape' => 'X12Version', ], ], ], 'X12Envelope' => [ 'type' => 'structure', 'members' => [ 'common' => [ 'shape' => 'X12OutboundEdiHeaders', ], 'wrapOptions' => [ 'shape' => 'WrapOptions', ], ], ], 'X12FunctionalAcknowledgment' => [ 'type' => 'string', 'enum' => [ 'DO_NOT_GENERATE', 'GENERATE_ALL_SEGMENTS', 'GENERATE_WITHOUT_TRANSACTION_SET_RESPONSE_LOOP', ], ], 'X12FunctionalGroupHeaders' => [ 'type' => 'structure', 'members' => [ 'applicationSenderCode' => [ 'shape' => 'X12ApplicationSenderCode', ], 'applicationReceiverCode' => [ 'shape' => 'X12ApplicationReceiverCode', ], 'responsibleAgencyCode' => [ 'shape' => 'X12ResponsibleAgencyCode', ], ], ], 'X12GS05TimeFormat' => [ 'type' => 'string', 'enum' => [ 'HHMM', 'HHMMSS', 'HHMMSSDD', ], ], 'X12IdQualifier' => [ 'type' => 'string', 'max' => 2, 'min' => 2, 'pattern' => '[a-zA-Z0-9]*', ], 'X12InboundEdiOptions' => [ 'type' => 'structure', 'members' => [ 'acknowledgmentOptions' => [ 'shape' => 'X12AcknowledgmentOptions', ], ], ], 'X12InterchangeControlHeaders' => [ 'type' => 'structure', 'members' => [ 'senderIdQualifier' => [ 'shape' => 'X12IdQualifier', ], 'senderId' => [ 'shape' => 'X12SenderId', ], 'receiverIdQualifier' => [ 'shape' => 'X12IdQualifier', ], 'receiverId' => [ 'shape' => 'X12ReceiverId', ], 'repetitionSeparator' => [ 'shape' => 'X12RepetitionSeparator', ], 'acknowledgmentRequestedCode' => [ 'shape' => 'X12AcknowledgmentRequestedCode', ], 'usageIndicatorCode' => [ 'shape' => 'X12UsageIndicatorCode', ], ], ], 'X12OutboundEdiHeaders' => [ 'type' => 'structure', 'members' => [ 'interchangeControlHeaders' => [ 'shape' => 'X12InterchangeControlHeaders', ], 'functionalGroupHeaders' => [ 'shape' => 'X12FunctionalGroupHeaders', ], 'delimiters' => [ 'shape' => 'X12Delimiters', ], 'validateEdi' => [ 'shape' => 'X12ValidateEdi', ], 'controlNumbers' => [ 'shape' => 'X12ControlNumbers', ], 'gs05TimeFormat' => [ 'shape' => 'X12GS05TimeFormat', ], ], ], 'X12ReceiverId' => [ 'type' => 'string', 'max' => 15, 'min' => 15, 'pattern' => '[a-zA-Z0-9 ]*', ], 'X12RepetitionSeparator' => [ 'type' => 'string', 'max' => 1, 'min' => 1, ], 'X12ResponsibleAgencyCode' => [ 'type' => 'string', 'max' => 2, 'min' => 1, 'pattern' => '[a-zA-Z0-9]*', ], 'X12SegmentTerminator' => [ 'type' => 'string', 'max' => 1, 'min' => 1, 'pattern' => '[!&\'()*+,\\-./:;?=%@\\[\\]_{}|<>~^`"]', ], 'X12SenderId' => [ 'type' => 'string', 'max' => 15, 'min' => 15, 'pattern' => '[a-zA-Z0-9 ]*', ], 'X12SplitBy' => [ 'type' => 'string', 'enum' => [ 'NONE', 'TRANSACTION', ], ], 'X12SplitOptions' => [ 'type' => 'structure', 'required' => [ 'splitBy', ], 'members' => [ 'splitBy' => [ 'shape' => 'X12SplitBy', ], ], ], 'X12TechnicalAcknowledgment' => [ 'type' => 'string', 'enum' => [ 'DO_NOT_GENERATE', 'GENERATE_ALL_SEGMENTS', ], ], 'X12TransactionSet' => [ 'type' => 'string', 'enum' => [ 'X12_100', 'X12_101', 'X12_102', 'X12_103', 'X12_104', 'X12_105', 'X12_106', 'X12_107', 'X12_108', 'X12_109', 'X12_110', 'X12_111', 'X12_112', 'X12_113', 'X12_120', 'X12_121', 'X12_124', 'X12_125', 'X12_126', 'X12_127', 'X12_128', 'X12_129', 'X12_130', 'X12_131', 'X12_132', 'X12_133', 'X12_135', 'X12_138', 'X12_139', 'X12_140', 'X12_141', 'X12_142', 'X12_143', 'X12_144', 'X12_146', 'X12_147', 'X12_148', 'X12_149', 'X12_150', 'X12_151', 'X12_152', 'X12_153', 'X12_154', 'X12_155', 'X12_157', 'X12_158', 'X12_159', 'X12_160', 'X12_161', 'X12_163', 'X12_170', 'X12_175', 'X12_176', 'X12_179', 'X12_180', 'X12_185', 'X12_186', 'X12_187', 'X12_188', 'X12_189', 'X12_190', 'X12_191', 'X12_194', 'X12_195', 'X12_196', 'X12_197', 'X12_198', 'X12_199', 'X12_200', 'X12_201', 'X12_202', 'X12_203', 'X12_204', 'X12_205', 'X12_206', 'X12_210', 'X12_211', 'X12_212', 'X12_213', 'X12_214', 'X12_215', 'X12_216', 'X12_217', 'X12_218', 'X12_219', 'X12_220', 'X12_222', 'X12_223', 'X12_224', 'X12_225', 'X12_227', 'X12_228', 'X12_240', 'X12_242', 'X12_244', 'X12_245', 'X12_248', 'X12_249', 'X12_250', 'X12_251', 'X12_252', 'X12_255', 'X12_256', 'X12_259', 'X12_260', 'X12_261', 'X12_262', 'X12_263', 'X12_264', 'X12_265', 'X12_266', 'X12_267', 'X12_268', 'X12_269', 'X12_270', 'X12_271', 'X12_272', 'X12_273', 'X12_274', 'X12_275', 'X12_276', 'X12_277', 'X12_278', 'X12_280', 'X12_283', 'X12_284', 'X12_285', 'X12_286', 'X12_288', 'X12_290', 'X12_300', 'X12_301', 'X12_303', 'X12_304', 'X12_309', 'X12_310', 'X12_311', 'X12_312', 'X12_313', 'X12_315', 'X12_317', 'X12_319', 'X12_322', 'X12_323', 'X12_324', 'X12_325', 'X12_326', 'X12_350', 'X12_352', 'X12_353', 'X12_354', 'X12_355', 'X12_356', 'X12_357', 'X12_358', 'X12_361', 'X12_362', 'X12_404', 'X12_410', 'X12_412', 'X12_414', 'X12_417', 'X12_418', 'X12_419', 'X12_420', 'X12_421', 'X12_422', 'X12_423', 'X12_424', 'X12_425', 'X12_426', 'X12_429', 'X12_431', 'X12_432', 'X12_433', 'X12_434', 'X12_435', 'X12_436', 'X12_437', 'X12_440', 'X12_451', 'X12_452', 'X12_453', 'X12_455', 'X12_456', 'X12_460', 'X12_463', 'X12_466', 'X12_468', 'X12_470', 'X12_475', 'X12_485', 'X12_486', 'X12_490', 'X12_492', 'X12_494', 'X12_500', 'X12_501', 'X12_503', 'X12_504', 'X12_511', 'X12_517', 'X12_521', 'X12_527', 'X12_536', 'X12_540', 'X12_561', 'X12_567', 'X12_568', 'X12_601', 'X12_602', 'X12_620', 'X12_625', 'X12_650', 'X12_715', 'X12_753', 'X12_754', 'X12_805', 'X12_806', 'X12_810', 'X12_811', 'X12_812', 'X12_813', 'X12_814', 'X12_815', 'X12_816', 'X12_818', 'X12_819', 'X12_820', 'X12_821', 'X12_822', 'X12_823', 'X12_824', 'X12_826', 'X12_827', 'X12_828', 'X12_829', 'X12_830', 'X12_831', 'X12_832', 'X12_833', 'X12_834', 'X12_835', 'X12_836', 'X12_837', 'X12_838', 'X12_839', 'X12_840', 'X12_841', 'X12_842', 'X12_843', 'X12_844', 'X12_845', 'X12_846', 'X12_847', 'X12_848', 'X12_849', 'X12_850', 'X12_851', 'X12_852', 'X12_853', 'X12_854', 'X12_855', 'X12_856', 'X12_857', 'X12_858', 'X12_859', 'X12_860', 'X12_861', 'X12_862', 'X12_863', 'X12_864', 'X12_865', 'X12_866', 'X12_867', 'X12_868', 'X12_869', 'X12_870', 'X12_871', 'X12_872', 'X12_873', 'X12_874', 'X12_875', 'X12_876', 'X12_877', 'X12_878', 'X12_879', 'X12_880', 'X12_881', 'X12_882', 'X12_883', 'X12_884', 'X12_885', 'X12_886', 'X12_887', 'X12_888', 'X12_889', 'X12_891', 'X12_893', 'X12_894', 'X12_895', 'X12_896', 'X12_920', 'X12_924', 'X12_925', 'X12_926', 'X12_928', 'X12_940', 'X12_943', 'X12_944', 'X12_945', 'X12_947', 'X12_980', 'X12_990', 'X12_993', 'X12_996', 'X12_997', 'X12_998', 'X12_999', 'X12_270_X279', 'X12_271_X279', 'X12_275_X210', 'X12_275_X211', 'X12_276_X212', 'X12_277_X212', 'X12_277_X214', 'X12_277_X364', 'X12_278_X217', 'X12_820_X218', 'X12_820_X306', 'X12_824_X186', 'X12_834_X220', 'X12_834_X307', 'X12_834_X318', 'X12_835_X221', 'X12_837_X222', 'X12_837_X223', 'X12_837_X224', 'X12_837_X291', 'X12_837_X292', 'X12_837_X298', 'X12_999_X231', ], ], 'X12UsageIndicatorCode' => [ 'type' => 'string', 'max' => 1, 'min' => 1, 'pattern' => '[a-zA-Z0-9]*', ], 'X12ValidateEdi' => [ 'type' => 'boolean', 'box' => true, ], 'X12Version' => [ 'type' => 'string', 'enum' => [ 'VERSION_4010', 'VERSION_4030', 'VERSION_4050', 'VERSION_4060', 'VERSION_5010', 'VERSION_5010_HIPAA', ], ], ],];
