<?php
// This file was auto-generated from sdk-root/src/data/arc-zonal-shift/2022-10-30/api-2.json
return [ 'version' => '2.0', 'metadata' => [ 'apiVersion' => '2022-10-30', 'auth' => [ 'aws.auth#sigv4', ], 'endpointPrefix' => 'arc-zonal-shift', 'protocol' => 'rest-json', 'protocols' => [ 'rest-json', ], 'serviceFullName' => 'AWS ARC - Zonal Shift', 'serviceId' => 'ARC Zonal Shift', 'signatureVersion' => 'v4', 'signingName' => 'arc-zonal-shift', 'uid' => 'arc-zonal-shift-2022-10-30', ], 'operations' => [ 'CancelPracticeRun' => [ 'name' => 'CancelPracticeRun', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/practiceruns/{zonalShiftId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CancelPracticeRunRequest', ], 'output' => [ 'shape' => 'CancelPracticeRunResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], 'idempotent' => true, ], 'CancelZonalShift' => [ 'name' => 'CancelZonalShift', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/zonalshifts/{zonalShiftId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'CancelZonalShiftRequest', ], 'output' => [ 'shape' => 'ZonalShift', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'CreatePracticeRunConfiguration' => [ 'name' => 'CreatePracticeRunConfiguration', 'http' => [ 'method' => 'POST', 'requestUri' => '/configuration', 'responseCode' => 201, ], 'input' => [ 'shape' => 'CreatePracticeRunConfigurationRequest', ], 'output' => [ 'shape' => 'CreatePracticeRunConfigurationResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'DeletePracticeRunConfiguration' => [ 'name' => 'DeletePracticeRunConfiguration', 'http' => [ 'method' => 'DELETE', 'requestUri' => '/configuration/{resourceIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'DeletePracticeRunConfigurationRequest', ], 'output' => [ 'shape' => 'DeletePracticeRunConfigurationResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], 'idempotent' => true, ], 'GetAutoshiftObserverNotificationStatus' => [ 'name' => 'GetAutoshiftObserverNotificationStatus', 'http' => [ 'method' => 'GET', 'requestUri' => '/autoshift-observer-notification', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetAutoshiftObserverNotificationStatusRequest', ], 'output' => [ 'shape' => 'GetAutoshiftObserverNotificationStatusResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], ], ], 'GetManagedResource' => [ 'name' => 'GetManagedResource', 'http' => [ 'method' => 'GET', 'requestUri' => '/managedresources/{resourceIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'GetManagedResourceRequest', ], 'output' => [ 'shape' => 'GetManagedResourceResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListAutoshifts' => [ 'name' => 'ListAutoshifts', 'http' => [ 'method' => 'GET', 'requestUri' => '/autoshifts', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListAutoshiftsRequest', ], 'output' => [ 'shape' => 'ListAutoshiftsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListManagedResources' => [ 'name' => 'ListManagedResources', 'http' => [ 'method' => 'GET', 'requestUri' => '/managedresources', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListManagedResourcesRequest', ], 'output' => [ 'shape' => 'ListManagedResourcesResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'ListZonalShifts' => [ 'name' => 'ListZonalShifts', 'http' => [ 'method' => 'GET', 'requestUri' => '/zonalshifts', 'responseCode' => 200, ], 'input' => [ 'shape' => 'ListZonalShiftsRequest', ], 'output' => [ 'shape' => 'ListZonalShiftsResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'StartPracticeRun' => [ 'name' => 'StartPracticeRun', 'http' => [ 'method' => 'POST', 'requestUri' => '/practiceruns', 'responseCode' => 200, ], 'input' => [ 'shape' => 'StartPracticeRunRequest', ], 'output' => [ 'shape' => 'StartPracticeRunResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'StartZonalShift' => [ 'name' => 'StartZonalShift', 'http' => [ 'method' => 'POST', 'requestUri' => '/zonalshifts', 'responseCode' => 201, ], 'input' => [ 'shape' => 'StartZonalShiftRequest', ], 'output' => [ 'shape' => 'ZonalShift', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'UpdateAutoshiftObserverNotificationStatus' => [ 'name' => 'UpdateAutoshiftObserverNotificationStatus', 'http' => [ 'method' => 'PUT', 'requestUri' => '/autoshift-observer-notification', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateAutoshiftObserverNotificationStatusRequest', ], 'output' => [ 'shape' => 'UpdateAutoshiftObserverNotificationStatusResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], 'idempotent' => true, ], 'UpdatePracticeRunConfiguration' => [ 'name' => 'UpdatePracticeRunConfiguration', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/configuration/{resourceIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdatePracticeRunConfigurationRequest', ], 'output' => [ 'shape' => 'UpdatePracticeRunConfigurationResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], 'UpdateZonalAutoshiftConfiguration' => [ 'name' => 'UpdateZonalAutoshiftConfiguration', 'http' => [ 'method' => 'PUT', 'requestUri' => '/managedresources/{resourceIdentifier}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateZonalAutoshiftConfigurationRequest', ], 'output' => [ 'shape' => 'UpdateZonalAutoshiftConfigurationResponse', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], 'idempotent' => true, ], 'UpdateZonalShift' => [ 'name' => 'UpdateZonalShift', 'http' => [ 'method' => 'PATCH', 'requestUri' => '/zonalshifts/{zonalShiftId}', 'responseCode' => 200, ], 'input' => [ 'shape' => 'UpdateZonalShiftRequest', ], 'output' => [ 'shape' => 'ZonalShift', ], 'errors' => [ [ 'shape' => 'InternalServerException', ], [ 'shape' => 'ConflictException', ], [ 'shape' => 'ResourceNotFoundException', ], [ 'shape' => 'ThrottlingException', ], [ 'shape' => 'AccessDeniedException', ], [ 'shape' => 'ValidationException', ], ], ], ], 'shapes' => [ 'AccessDeniedException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 403, 'senderFault' => true, ], 'exception' => true, ], 'AppliedStatus' => [ 'type' => 'string', 'enum' => [ 'APPLIED', 'NOT_APPLIED', ], ], 'AppliedWeights' => [ 'type' => 'map', 'key' => [ 'shape' => 'AvailabilityZone', ], 'value' => [ 'shape' => 'Weight', ], ], 'AutoshiftAppliedStatus' => [ 'type' => 'string', 'enum' => [ 'APPLIED', 'NOT_APPLIED', ], ], 'AutoshiftExecutionStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'COMPLETED', ], ], 'AutoshiftInResource' => [ 'type' => 'structure', 'required' => [ 'appliedStatus', 'awayFrom', 'startTime', ], 'members' => [ 'appliedStatus' => [ 'shape' => 'AutoshiftAppliedStatus', ], 'awayFrom' => [ 'shape' => 'AvailabilityZone', ], 'startTime' => [ 'shape' => 'StartTime', ], ], ], 'AutoshiftObserverNotificationStatus' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'AutoshiftSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'AutoshiftSummary', ], ], 'AutoshiftSummary' => [ 'type' => 'structure', 'required' => [ 'awayFrom', 'startTime', 'status', ], 'members' => [ 'awayFrom' => [ 'shape' => 'AvailabilityZone', ], 'endTime' => [ 'shape' => 'ExpiryTime', ], 'startTime' => [ 'shape' => 'StartTime', ], 'status' => [ 'shape' => 'AutoshiftExecutionStatus', ], ], ], 'AutoshiftsInResource' => [ 'type' => 'list', 'member' => [ 'shape' => 'AutoshiftInResource', ], ], 'AvailabilityZone' => [ 'type' => 'string', 'max' => 20, 'min' => 0, ], 'AvailabilityZones' => [ 'type' => 'list', 'member' => [ 'shape' => 'AvailabilityZone', ], ], 'BlockedDate' => [ 'type' => 'string', 'max' => 10, 'min' => 10, 'pattern' => '[0-9]{4}-[0-9]{2}-[0-9]{2}', ], 'BlockedDates' => [ 'type' => 'list', 'member' => [ 'shape' => 'BlockedDate', ], 'max' => 15, 'min' => 0, ], 'BlockedWindow' => [ 'type' => 'string', 'max' => 19, 'min' => 19, 'pattern' => '(Mon|Tue|Wed|Thu|Fri|Sat|Sun):[0-9]{2}:[0-9]{2}-(Mon|Tue|Wed|Thu|Fri|Sat|Sun):[0-9]{2}:[0-9]{2}', ], 'BlockedWindows' => [ 'type' => 'list', 'member' => [ 'shape' => 'BlockedWindow', ], 'max' => 15, 'min' => 0, ], 'CancelPracticeRunRequest' => [ 'type' => 'structure', 'required' => [ 'zonalShiftId', ], 'members' => [ 'zonalShiftId' => [ 'shape' => 'ZonalShiftId', 'location' => 'uri', 'locationName' => 'zonalShiftId', ], ], ], 'CancelPracticeRunResponse' => [ 'type' => 'structure', 'required' => [ 'zonalShiftId', 'resourceIdentifier', 'awayFrom', 'expiryTime', 'startTime', 'status', 'comment', ], 'members' => [ 'zonalShiftId' => [ 'shape' => 'ZonalShiftId', ], 'resourceIdentifier' => [ 'shape' => 'ResourceIdentifier', ], 'awayFrom' => [ 'shape' => 'AvailabilityZone', ], 'expiryTime' => [ 'shape' => 'ExpiryTime', ], 'startTime' => [ 'shape' => 'StartTime', ], 'status' => [ 'shape' => 'ZonalShiftStatus', ], 'comment' => [ 'shape' => 'ZonalShiftComment', ], ], ], 'CancelZonalShiftRequest' => [ 'type' => 'structure', 'required' => [ 'zonalShiftId', ], 'members' => [ 'zonalShiftId' => [ 'shape' => 'ZonalShiftId', 'location' => 'uri', 'locationName' => 'zonalShiftId', ], ], ], 'ConflictException' => [ 'type' => 'structure', 'required' => [ 'message', 'reason', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'reason' => [ 'shape' => 'ConflictExceptionReason', ], 'zonalShiftId' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 409, 'senderFault' => true, ], 'exception' => true, ], 'ConflictExceptionReason' => [ 'type' => 'string', 'enum' => [ 'ZonalShiftAlreadyExists', 'ZonalShiftStatusNotActive', 'SimultaneousZonalShiftsConflict', 'PracticeConfigurationAlreadyExists', 'AutoShiftEnabled', 'PracticeConfigurationDoesNotExist', 'ZonalAutoshiftActive', 'PracticeOutcomeAlarmsRed', 'PracticeBlockingAlarmsRed', 'PracticeInBlockedDates', 'PracticeInBlockedWindows', ], ], 'ControlCondition' => [ 'type' => 'structure', 'required' => [ 'type', 'alarmIdentifier', ], 'members' => [ 'type' => [ 'shape' => 'ControlConditionType', ], 'alarmIdentifier' => [ 'shape' => 'MetricIdentifier', ], ], ], 'ControlConditionType' => [ 'type' => 'string', 'enum' => [ 'CLOUDWATCH', ], ], 'ControlConditions' => [ 'type' => 'list', 'member' => [ 'shape' => 'ControlCondition', ], 'max' => 1, 'min' => 1, ], 'CreatePracticeRunConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'resourceIdentifier', 'outcomeAlarms', ], 'members' => [ 'resourceIdentifier' => [ 'shape' => 'ResourceIdentifier', ], 'blockedWindows' => [ 'shape' => 'BlockedWindows', ], 'blockedDates' => [ 'shape' => 'BlockedDates', ], 'blockingAlarms' => [ 'shape' => 'ControlConditions', ], 'outcomeAlarms' => [ 'shape' => 'ControlConditions', ], ], ], 'CreatePracticeRunConfigurationResponse' => [ 'type' => 'structure', 'required' => [ 'arn', 'name', 'zonalAutoshiftStatus', 'practiceRunConfiguration', ], 'members' => [ 'arn' => [ 'shape' => 'ResourceArn', ], 'name' => [ 'shape' => 'ResourceName', ], 'zonalAutoshiftStatus' => [ 'shape' => 'ZonalAutoshiftStatus', ], 'practiceRunConfiguration' => [ 'shape' => 'PracticeRunConfiguration', ], ], ], 'DeletePracticeRunConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'resourceIdentifier', ], 'members' => [ 'resourceIdentifier' => [ 'shape' => 'ResourceIdentifier', 'location' => 'uri', 'locationName' => 'resourceIdentifier', ], ], ], 'DeletePracticeRunConfigurationResponse' => [ 'type' => 'structure', 'required' => [ 'arn', 'name', 'zonalAutoshiftStatus', ], 'members' => [ 'arn' => [ 'shape' => 'ResourceArn', ], 'name' => [ 'shape' => 'ResourceName', ], 'zonalAutoshiftStatus' => [ 'shape' => 'ZonalAutoshiftStatus', ], ], ], 'ExpiresIn' => [ 'type' => 'string', 'max' => 5, 'min' => 2, 'pattern' => '([1-9][0-9]*)(m|h)', ], 'ExpiryTime' => [ 'type' => 'timestamp', ], 'GetAutoshiftObserverNotificationStatusRequest' => [ 'type' => 'structure', 'members' => [], ], 'GetAutoshiftObserverNotificationStatusResponse' => [ 'type' => 'structure', 'required' => [ 'status', ], 'members' => [ 'status' => [ 'shape' => 'AutoshiftObserverNotificationStatus', ], ], ], 'GetManagedResourceRequest' => [ 'type' => 'structure', 'required' => [ 'resourceIdentifier', ], 'members' => [ 'resourceIdentifier' => [ 'shape' => 'ResourceIdentifier', 'location' => 'uri', 'locationName' => 'resourceIdentifier', ], ], ], 'GetManagedResourceResponse' => [ 'type' => 'structure', 'required' => [ 'appliedWeights', 'zonalShifts', ], 'members' => [ 'arn' => [ 'shape' => 'ResourceArn', ], 'name' => [ 'shape' => 'ResourceName', ], 'appliedWeights' => [ 'shape' => 'AppliedWeights', ], 'zonalShifts' => [ 'shape' => 'ZonalShiftsInResource', ], 'autoshifts' => [ 'shape' => 'AutoshiftsInResource', ], 'practiceRunConfiguration' => [ 'shape' => 'PracticeRunConfiguration', ], 'zonalAutoshiftStatus' => [ 'shape' => 'ZonalAutoshiftStatus', ], ], ], 'InternalServerException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 500, ], 'exception' => true, 'fault' => true, ], 'ListAutoshiftsRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'status' => [ 'shape' => 'AutoshiftExecutionStatus', 'location' => 'querystring', 'locationName' => 'status', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListAutoshiftsResponse' => [ 'type' => 'structure', 'members' => [ 'items' => [ 'shape' => 'AutoshiftSummaries', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListManagedResourcesRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], ], ], 'ListManagedResourcesResponse' => [ 'type' => 'structure', 'required' => [ 'items', ], 'members' => [ 'items' => [ 'shape' => 'ManagedResourceSummaries', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ListZonalShiftsRequest' => [ 'type' => 'structure', 'members' => [ 'nextToken' => [ 'shape' => 'String', 'location' => 'querystring', 'locationName' => 'nextToken', ], 'status' => [ 'shape' => 'ZonalShiftStatus', 'location' => 'querystring', 'locationName' => 'status', ], 'maxResults' => [ 'shape' => 'MaxResults', 'location' => 'querystring', 'locationName' => 'maxResults', ], 'resourceIdentifier' => [ 'shape' => 'ResourceIdentifier', 'location' => 'querystring', 'locationName' => 'resourceIdentifier', ], ], ], 'ListZonalShiftsResponse' => [ 'type' => 'structure', 'members' => [ 'items' => [ 'shape' => 'ZonalShiftSummaries', ], 'nextToken' => [ 'shape' => 'String', ], ], ], 'ManagedResourceSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'ManagedResourceSummary', ], ], 'ManagedResourceSummary' => [ 'type' => 'structure', 'required' => [ 'availabilityZones', ], 'members' => [ 'arn' => [ 'shape' => 'ResourceArn', ], 'name' => [ 'shape' => 'ResourceName', ], 'availabilityZones' => [ 'shape' => 'AvailabilityZones', ], 'appliedWeights' => [ 'shape' => 'AppliedWeights', ], 'zonalShifts' => [ 'shape' => 'ZonalShiftsInResource', ], 'autoshifts' => [ 'shape' => 'AutoshiftsInResource', ], 'zonalAutoshiftStatus' => [ 'shape' => 'ZonalAutoshiftStatus', ], 'practiceRunStatus' => [ 'shape' => 'ZonalAutoshiftStatus', ], ], ], 'MaxResults' => [ 'type' => 'integer', 'box' => true, 'max' => 100, 'min' => 1, ], 'MetricIdentifier' => [ 'type' => 'string', 'max' => 1024, 'min' => 8, 'pattern' => '.*', ], 'PracticeRunConfiguration' => [ 'type' => 'structure', 'required' => [ 'outcomeAlarms', ], 'members' => [ 'blockingAlarms' => [ 'shape' => 'ControlConditions', ], 'outcomeAlarms' => [ 'shape' => 'ControlConditions', ], 'blockedWindows' => [ 'shape' => 'BlockedWindows', ], 'blockedDates' => [ 'shape' => 'BlockedDates', ], ], ], 'PracticeRunOutcome' => [ 'type' => 'string', 'enum' => [ 'FAILED', 'INTERRUPTED', 'PENDING', 'SUCCEEDED', 'CAPACITY_CHECK_FAILED', ], ], 'ResourceArn' => [ 'type' => 'string', 'max' => 1024, 'min' => 8, 'pattern' => 'arn:.*', ], 'ResourceIdentifier' => [ 'type' => 'string', 'max' => 1024, 'min' => 8, ], 'ResourceName' => [ 'type' => 'string', 'max' => 256, 'min' => 1, ], 'ResourceNotFoundException' => [ 'type' => 'structure', 'required' => [ 'message', ], 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 404, 'senderFault' => true, ], 'exception' => true, ], 'ShiftType' => [ 'type' => 'string', 'enum' => [ 'ZONAL_SHIFT', 'PRACTICE_RUN', 'FIS_EXPERIMENT', 'ZONAL_AUTOSHIFT', ], ], 'StartPracticeRunRequest' => [ 'type' => 'structure', 'required' => [ 'resourceIdentifier', 'awayFrom', 'comment', ], 'members' => [ 'resourceIdentifier' => [ 'shape' => 'ResourceIdentifier', ], 'awayFrom' => [ 'shape' => 'AvailabilityZone', ], 'comment' => [ 'shape' => 'ZonalShiftComment', ], ], ], 'StartPracticeRunResponse' => [ 'type' => 'structure', 'required' => [ 'zonalShiftId', 'resourceIdentifier', 'awayFrom', 'expiryTime', 'startTime', 'status', 'comment', ], 'members' => [ 'zonalShiftId' => [ 'shape' => 'ZonalShiftId', ], 'resourceIdentifier' => [ 'shape' => 'ResourceIdentifier', ], 'awayFrom' => [ 'shape' => 'AvailabilityZone', ], 'expiryTime' => [ 'shape' => 'ExpiryTime', ], 'startTime' => [ 'shape' => 'StartTime', ], 'status' => [ 'shape' => 'ZonalShiftStatus', ], 'comment' => [ 'shape' => 'ZonalShiftComment', ], ], ], 'StartTime' => [ 'type' => 'timestamp', ], 'StartZonalShiftRequest' => [ 'type' => 'structure', 'required' => [ 'resourceIdentifier', 'awayFrom', 'expiresIn', 'comment', ], 'members' => [ 'resourceIdentifier' => [ 'shape' => 'ResourceIdentifier', ], 'awayFrom' => [ 'shape' => 'AvailabilityZone', ], 'expiresIn' => [ 'shape' => 'ExpiresIn', ], 'comment' => [ 'shape' => 'ZonalShiftComment', ], ], ], 'String' => [ 'type' => 'string', ], 'ThrottlingException' => [ 'type' => 'structure', 'members' => [ 'message' => [ 'shape' => 'String', ], ], 'error' => [ 'httpStatusCode' => 429, 'senderFault' => true, ], 'exception' => true, ], 'UpdateAutoshiftObserverNotificationStatusRequest' => [ 'type' => 'structure', 'required' => [ 'status', ], 'members' => [ 'status' => [ 'shape' => 'AutoshiftObserverNotificationStatus', ], ], ], 'UpdateAutoshiftObserverNotificationStatusResponse' => [ 'type' => 'structure', 'required' => [ 'status', ], 'members' => [ 'status' => [ 'shape' => 'AutoshiftObserverNotificationStatus', ], ], ], 'UpdatePracticeRunConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'resourceIdentifier', ], 'members' => [ 'resourceIdentifier' => [ 'shape' => 'ResourceIdentifier', 'location' => 'uri', 'locationName' => 'resourceIdentifier', ], 'blockedWindows' => [ 'shape' => 'BlockedWindows', ], 'blockedDates' => [ 'shape' => 'BlockedDates', ], 'blockingAlarms' => [ 'shape' => 'ControlConditions', ], 'outcomeAlarms' => [ 'shape' => 'ControlConditions', ], ], ], 'UpdatePracticeRunConfigurationResponse' => [ 'type' => 'structure', 'required' => [ 'arn', 'name', 'zonalAutoshiftStatus', 'practiceRunConfiguration', ], 'members' => [ 'arn' => [ 'shape' => 'ResourceArn', ], 'name' => [ 'shape' => 'ResourceName', ], 'zonalAutoshiftStatus' => [ 'shape' => 'ZonalAutoshiftStatus', ], 'practiceRunConfiguration' => [ 'shape' => 'PracticeRunConfiguration', ], ], ], 'UpdateZonalAutoshiftConfigurationRequest' => [ 'type' => 'structure', 'required' => [ 'resourceIdentifier', 'zonalAutoshiftStatus', ], 'members' => [ 'resourceIdentifier' => [ 'shape' => 'ResourceIdentifier', 'location' => 'uri', 'locationName' => 'resourceIdentifier', ], 'zonalAutoshiftStatus' => [ 'shape' => 'ZonalAutoshiftStatus', ], ], ], 'UpdateZonalAutoshiftConfigurationResponse' => [ 'type' => 'structure', 'required' => [ 'resourceIdentifier', 'zonalAutoshiftStatus', ], 'members' => [ 'resourceIdentifier' => [ 'shape' => 'ResourceIdentifier', ], 'zonalAutoshiftStatus' => [ 'shape' => 'ZonalAutoshiftStatus', ], ], ], 'UpdateZonalShiftRequest' => [ 'type' => 'structure', 'required' => [ 'zonalShiftId', ], 'members' => [ 'zonalShiftId' => [ 'shape' => 'ZonalShiftId', 'location' => 'uri', 'locationName' => 'zonalShiftId', ], 'comment' => [ 'shape' => 'ZonalShiftComment', ], 'expiresIn' => [ 'shape' => 'ExpiresIn', ], ], ], 'ValidationException' => [ 'type' => 'structure', 'required' => [ 'message', 'reason', ], 'members' => [ 'message' => [ 'shape' => 'String', ], 'reason' => [ 'shape' => 'ValidationExceptionReason', ], ], 'error' => [ 'httpStatusCode' => 400, 'senderFault' => true, ], 'exception' => true, ], 'ValidationExceptionReason' => [ 'type' => 'string', 'enum' => [ 'InvalidExpiresIn', 'InvalidStatus', 'MissingValue', 'InvalidToken', 'InvalidResourceIdentifier', 'InvalidAz', 'UnsupportedAz', 'InvalidAlarmCondition', 'InvalidConditionType', 'InvalidPracticeBlocker', 'FISExperimentUpdateNotAllowed', 'AutoshiftUpdateNotAllowed', 'UnsupportedPracticeCancelShiftType', ], ], 'Weight' => [ 'type' => 'float', 'box' => true, 'max' => 1.0, 'min' => 0.0, ], 'ZonalAutoshiftStatus' => [ 'type' => 'string', 'enum' => [ 'ENABLED', 'DISABLED', ], ], 'ZonalShift' => [ 'type' => 'structure', 'required' => [ 'zonalShiftId', 'resourceIdentifier', 'awayFrom', 'expiryTime', 'startTime', 'status', 'comment', ], 'members' => [ 'zonalShiftId' => [ 'shape' => 'ZonalShiftId', ], 'resourceIdentifier' => [ 'shape' => 'ResourceIdentifier', ], 'awayFrom' => [ 'shape' => 'AvailabilityZone', ], 'expiryTime' => [ 'shape' => 'ExpiryTime', ], 'startTime' => [ 'shape' => 'StartTime', ], 'status' => [ 'shape' => 'ZonalShiftStatus', ], 'comment' => [ 'shape' => 'ZonalShiftComment', ], ], ], 'ZonalShiftComment' => [ 'type' => 'string', 'max' => 128, 'min' => 0, ], 'ZonalShiftId' => [ 'type' => 'string', 'max' => 36, 'min' => 6, 'pattern' => '[A-Za-z0-9-]+', ], 'ZonalShiftInResource' => [ 'type' => 'structure', 'required' => [ 'appliedStatus', 'zonalShiftId', 'resourceIdentifier', 'awayFrom', 'expiryTime', 'startTime', 'comment', ], 'members' => [ 'appliedStatus' => [ 'shape' => 'AppliedStatus', ], 'zonalShiftId' => [ 'shape' => 'ZonalShiftId', ], 'resourceIdentifier' => [ 'shape' => 'ResourceIdentifier', ], 'awayFrom' => [ 'shape' => 'AvailabilityZone', ], 'expiryTime' => [ 'shape' => 'ExpiryTime', ], 'startTime' => [ 'shape' => 'StartTime', ], 'comment' => [ 'shape' => 'ZonalShiftComment', ], 'shiftType' => [ 'shape' => 'ShiftType', ], 'practiceRunOutcome' => [ 'shape' => 'PracticeRunOutcome', ], ], ], 'ZonalShiftStatus' => [ 'type' => 'string', 'enum' => [ 'ACTIVE', 'EXPIRED', 'CANCELED', ], ], 'ZonalShiftSummaries' => [ 'type' => 'list', 'member' => [ 'shape' => 'ZonalShiftSummary', ], ], 'ZonalShiftSummary' => [ 'type' => 'structure', 'required' => [ 'zonalShiftId', 'resourceIdentifier', 'awayFrom', 'expiryTime', 'startTime', 'status', 'comment', ], 'members' => [ 'zonalShiftId' => [ 'shape' => 'ZonalShiftId', ], 'resourceIdentifier' => [ 'shape' => 'ResourceIdentifier', ], 'awayFrom' => [ 'shape' => 'AvailabilityZone', ], 'expiryTime' => [ 'shape' => 'ExpiryTime', ], 'startTime' => [ 'shape' => 'StartTime', ], 'status' => [ 'shape' => 'ZonalShiftStatus', ], 'comment' => [ 'shape' => 'ZonalShiftComment', ], 'shiftType' => [ 'shape' => 'ShiftType', ], 'practiceRunOutcome' => [ 'shape' => 'PracticeRunOutcome', ], ], ], 'ZonalShiftsInResource' => [ 'type' => 'list', 'member' => [ 'shape' => 'ZonalShiftInResource', ], ], ],];
